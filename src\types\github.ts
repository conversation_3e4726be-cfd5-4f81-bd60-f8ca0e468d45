
export interface GitHubRepoInfo {
  url?: string;
  owner?: string;
  name?: string;
  branch?: string;
  commitSha?: string;
  path?: string;
  programmingLanguage?: string;
}

export interface ParsedGitHubUrl {
  owner: string;
  repo: string;
}

export interface GitHubRepoConfig {
  owner: string;
  repository: string;
  branch: string;
  path: string;
  commit: string;
  programmingLanguage?: string;
}
