import React, { useState } from "react";
import { <PERSON>, useNavigate } from "react-router-dom";

import { toast } from "@/hooks/use-toast";
import { Input } from "@/components/ui/input";

import GitHubRepoCrawler from "@/components/GitHubRepoCrawler";
import { Button } from "@/components/ui/button";
import { useTrialStatus } from "@/hooks/useTrialStatus";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertTriangle } from "lucide-react";

import { SubscriptionPlans } from "@/components/subscription/SubscriptionPlans";
import { useSubscription } from "@/hooks/useSubscription";
import { Skeleton } from "@/components/ui/skeleton";

// Default file patterns
const DEFAULT_INCLUDE_PATTERNS = [
  "*.py",
  "*.js",
  "*.jsx",
  "*.ts",
  "*.tsx",
  "*.go",
  "*.java",
  "*.pyi",
  "*.pyx",
  "*.c",
  "*.cc",
  "*.cpp",
  "*.h",
  "*.md",
  "*.rst",
  "Dockerfile",
  "Makefile",
  "*.yaml",
  "*.yml",
];

const DEFAULT_EXCLUDE_PATTERNS = [
  "assets/*",
  "data/*",
  "examples/*",
  "images/*",
  "public/*",
  "static/*",
  "temp/*",
  "docs/*",
  "venv/*",
  ".venv/*",
  "*test*",
  "tests/*",
  "docs/*",
  "examples/*",
  "v1/*",
  "dist/*",
  "build/*",
  "experimental/*",
  "deprecated/*",
  "misc/*",
  "legacy/*",
  ".git/*",
  ".github/*",
  ".next/*",
  ".vscode/*",
  "obj/*",
  "bin/*",
  "node_modules/*",
  "*.log",
];

const Create = () => {
  const navigate = useNavigate();
  const { trialStatus, loading } = useTrialStatus();

  const [repoUrl, setRepoUrl] = useState(
    "https://github.com/EarthShaping/testautocode"
  );
  const [isPrivateRepo, setIsPrivateRepo] = useState(false);
  const [fileSize, setFileSize] = useState(100);
  const [showAdvancedOptions, setShowAdvancedOptions] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);

  const [isCrawling, setIsCrawling] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState<string[]>([]);
  const { subscribed, loading: subscriptionLoading } = useSubscription();
  const handleSelectedFilesChange = (files: string[]) => {
    setSelectedFiles(files);
  };




  const parsePatterns = (patternsString: string): string[] => {
    return patternsString
      .split(",")
      .map((pattern) => pattern.trim())
      .filter((pattern) => pattern.length > 0);
  };

  const handleStartGeneration = (e: React.FormEvent) => {
    e.preventDefault();
    toast({
      title: "Starting generation",
      description: "Your tutorial is being generated...",
    });
    setIsGenerating(true);

    // Get form values
    const targetAudience =
      (document.getElementById("target-audience") as HTMLSelectElement)
        ?.value || "intermediate";
    const contentLanguage =
      (document.getElementById("content-language") as HTMLSelectElement)
        ?.value || "english";
    const maxAbstractions = parseInt(
      (document.getElementById("max-abstractions") as HTMLInputElement)
        ?.value || "10"
    );
    const tutorialFormat =
      (document.getElementById("tutorial-format") as HTMLSelectElement)
        ?.value || "markdown";
    const includeDiagrams =
      (document.getElementById("include-diagrams") as HTMLInputElement)
        ?.checked || false;
    const includeExamples =
      (document.getElementById("include-examples") as HTMLInputElement)
        ?.checked || false;
    const includeExercises =
      (document.getElementById("include-exercises") as HTMLInputElement)
        ?.checked || false;
    const includePatterns =
      (document.getElementById("include-patterns") as HTMLInputElement)
        ?.value || "**/*.ts, **/*.js";
    const excludePatterns =
      (document.getElementById("exclude-patterns") as HTMLInputElement)
        ?.value || "node_modules/**, *.test.js";

    // Get GitHub token if private repo
    let githubToken = "";
    if (isPrivateRepo) {
      githubToken =
        (document.getElementById("github-token") as HTMLInputElement)?.value ||
        "";
    }

    // Advanced options
    let advancedOptions = {};
    if (showAdvancedOptions) {
      const maxTokens = parseInt(
        (document.getElementById("max-tokens") as HTMLInputElement)?.value ||
          "100000"
      );
      const modelVersion =
        (document.getElementById("model-version") as HTMLSelectElement)
          ?.value || "UX Pilot-3-7-sonnet";
      const cacheDuration = parseInt(
        (document.getElementById("cache-duration") as HTMLSelectElement)
          ?.value || "7"
      );
      const temperature = parseFloat(
        (document.getElementById("temperature") as HTMLInputElement)?.value ||
          "0.7"
      );

      advancedOptions = {
        maxTokens,
        modelVersion,
        cacheDuration,
        temperature,
      };
    }

    // Navigate to tutorial creation status page with parameters
    navigate("/dashboard/tutorial-creation-status", {
      state: {
        repoUrl,
        isPrivateRepo,
        githubToken,
        fileSize,
        includePatterns,
        excludePatterns,
        selectedFiles,
        targetAudience,
        contentLanguage,
        maxAbstractions,
        tutorialFormat,
        includeDiagrams,
        includeExamples,
        includeExercises,
        advancedOptions,
      },
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!repoUrl) {
      toast({
        title: "Repository URL required",
        description: "Please enter a valid GitHub repository URL.",
        variant: "destructive",
      });
      return;
    }

    // Check trial limits before submitting
    if (!loading && trialStatus.isInTrial && !trialStatus.canCreateTutorial) {
      toast({
        title: "Trial Limit Reached",
        description: `You've created ${trialStatus.tutorialsCreated} of ${trialStatus.tutorialsLimit} allowed tutorials. Upgrade to create more.`,
        variant: "destructive",
      });
      return;
    }

    setIsCrawling(true);
  };

  const [temperature, setTemperature] = useState(0.7);

  const handleReset = (e: React.FormEvent) => {
    setIsCrawling(false);
    setIsGenerating(false);
    e.preventDefault();
  };

  const isAnalyzeDisabled =
    !repoUrl ||
    (!loading && trialStatus.isInTrial && !trialStatus.canCreateTutorial);
  const isGenerateDisabled =
    !loading && trialStatus.isInTrial && !trialStatus.canCreateTutorial;

  return (
    <>
      {/* Page Title */}
      <div className="mb-8 text-center">
        <h1 className="text-3xl md:text-4xl font-bold text-gray-800 mb-3">
          Generate Tutorials from GitHub Repositories
        </h1>
        <p className="text-gray-600 max-w-3xl mx-auto">
          Transform any GitHub repository into a comprehensive,
          beginner-friendly tutorial with the power of AI.
        </p>
      </div>

    {subscriptionLoading ? (
        <div className="flex justify-center items-center h-12">
          <Skeleton className="h-4 w-1/5" />
        </div>
      ) : (
        !subscribed && (
          <div className="mt-12">
            <h2 className="text-2xl font-bold mb-6 text-center">
              Upgrade Your Plan
            </h2>
            <SubscriptionPlans />
          </div>
        )   
      )
}


{!subscriptionLoading && subscribed && (
      <div className="flex flex-col lg:flex-row gap-8">
        {/* Main Form Section */}
        <div className="w-full lg:w-2/3 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-xl font-semibold mb-6 text-gray-800">
            Configure Your Tutorial
          </h2>

          {/* Trial Limit Warning */}
          {!loading &&
            trialStatus.isInTrial &&
            !trialStatus.canCreateTutorial && (
              <Alert className="mb-6 border-orange-200 bg-orange-50">
                <AlertTriangle className="h-4 w-4 text-orange-600" />
                <AlertDescription className="text-orange-800">
                  You've reached your trial limit of{" "}
                  {trialStatus.tutorialsLimit} tutorials. Upgrade your account
                  to create unlimited tutorials.
                </AlertDescription>
              </Alert>
            )}

          {/* Form Github  */}

          {!isCrawling && (
            <form onSubmit={handleSubmit}>
              {/* Repository URL Input */}
              <div className="mb-6">
                <label
                  htmlFor="repo-url"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  GitHub Repository URL
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <i className="fa-brands fa-github text-gray-400"></i>
                  </div>
                  <input
                    type="text"
                    id="repo-url"
                    value={repoUrl}
                    onChange={(e) => setRepoUrl(e.target.value)}
                    className="block w-full pl-10 pr-12 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
                    placeholder="https://github.com/username/repository"
                  />
                  <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                    <button
                      type="button"
                      className="text-tutorial-primary hover:text-blue-700"
                      onClick={() => {
                        navigator.clipboard.readText().then((text) => {
                          if (text.includes("github.com")) setRepoUrl(text);
                        });
                      }}
                    >
                      <i className="fa-solid fa-paste"></i>
                    </button>
                  </div>
                </div>
              </div>

              {/* Repository Access */}
              <div className="mb-6">
                <div className="flex items-center justify-between mb-1">
                  <label className="block text-sm font-medium text-gray-700">
                    Repository Access
                  </label>
                  <span className="text-xs text-gray-500">
                    Private repos require authentication
                  </span>
                </div>
                <div className="flex space-x-4">
                  <div className="flex items-center">
                    <input
                      type="radio"
                      id="public-repo"
                      name="repo-access"
                      checked={!isPrivateRepo}
                      onChange={() => setIsPrivateRepo(false)}
                      className="h-4 w-4 text-tutorial-primary focus:ring-tutorial-primary border-gray-300"
                    />
                    <label
                      htmlFor="public-repo"
                      className="ml-2 block text-sm text-gray-700"
                    >
                      Public
                    </label>
                  </div>
                  <div className="flex items-center">
                    <input
                      type="radio"
                      id="private-repo"
                      name="repo-access"
                      checked={isPrivateRepo}
                      onChange={() => setIsPrivateRepo(true)}
                      className="h-4 w-4 text-tutorial-primary focus:ring-tutorial-primary border-gray-300"
                    />
                    <label
                      htmlFor="private-repo"
                      className="ml-2 block text-sm text-gray-700"
                    >
                      Private
                    </label>
                  </div>
                </div>
              </div>

              {/* Authentication Section */}
              {isPrivateRepo && (
                <div className="mb-6">
                  <div className="p-4 bg-gray-50 rounded-md border border-gray-200">
                    <h3 className="text-sm font-medium text-gray-700 mb-3">
                      Authentication Options
                    </h3>
                    <div className="space-y-4">
                      <div>
                        <div className="flex items-center mb-2">
                          <input
                            type="radio"
                            id="personal-token"
                            name="auth-method"
                            defaultChecked
                            className="h-4 w-4 text-tutorial-primary focus:ring-tutorial-primary border-gray-300"
                          />
                          <label
                            htmlFor="personal-token"
                            className="ml-2 block text-sm text-gray-700"
                          >
                            Personal Access Token
                          </label>
                        </div>
                        <div className="relative">
                          <input
                            type="password"
                            id="github-token"
                            className="block w-full pr-10 py-2 border border-gray-300 rounded-md focus:ring-tutorial-primary focus:border-tutorial-primary"
                            placeholder="ghp_xxxxxxxxxxxxxxxxxxxx"
                          />
                          <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                            <button
                              type="button"
                              className="text-gray-400 hover:text-gray-600"
                            >
                              <i className="fa-regular fa-eye"></i>
                            </button>
                          </div>
                        </div>
                        <p className="mt-1 text-xs text-gray-500">
                          Needs 'repo' scope for private repositories
                        </p>
                      </div>

                      <div>
                        <div className="flex items-center">
                          <input
                            type="radio"
                            id="oauth"
                            name="auth-method"
                            className="h-4 w-4 text-tutorial-primary focus:ring-tutorial-primary border-gray-300"
                          />
                          <label
                            htmlFor="oauth"
                            className="ml-2 block text-sm text-gray-700"
                          >
                            GitHub OAuth
                          </label>
                        </div>
                        <button
                          type="button"
                          className="mt-2 flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                        >
                          <i className="fa-brands fa-github mr-2"></i>
                          Sign in with GitHub
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* File Patterns Section */}
              <div className="mb-6">
                <h3 className="text-sm font-medium text-gray-700 mb-3">
                  File Selection
                </h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  {/* Include Patterns */}
                  <div>
                    <label
                      htmlFor="include-patterns"
                      className="block text-sm font-medium text-gray-700 mb-1"
                    >
                      Include Patterns
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i className="fa-solid fa-check-circle text-green-500"></i>
                      </div>
                      <Input
                        id="include-patterns"
                        defaultValue={DEFAULT_INCLUDE_PATTERNS}
                        type="text"
                        className="block w-full pl-10 py-2 border border-gray-300 rounded-md focus:ring-tutorial-primary focus:border-tutorial-primary"
                        placeholder="*.js, src/**/*.jsx"
                      />
                    </div>
                    <p className="mt-1 text-xs text-gray-500">
                      Comma-separated glob patterns to include
                    </p>
                  </div>

                  {/* Exclude Patterns */}
                  <div>
                    <label
                      htmlFor="exclude-patterns"
                      className="block text-sm font-medium text-gray-700 mb-1"
                    >
                      Exclude Patterns
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i className="fa-solid fa-ban text-red-500"></i>
                      </div>
                      <Input
                        id="exclude-patterns"
                        defaultValue={DEFAULT_EXCLUDE_PATTERNS}
                        type="text"
                        className="block w-full pl-10 py-2 border border-gray-300 rounded-md focus:ring-tutorial-primary focus:border-tutorial-primary"
                        placeholder="node_modules/**, *.test.js"
                      />
                    </div>
                    <p className="mt-1 text-xs text-gray-500">
                      Patterns to exclude from analysis
                    </p>
                  </div>
                </div>

                {/* File Size Limit */}
                <div className="mb-4">
                  <div className="flex items-center justify-between">
                    <label
                      htmlFor="file-size-limit"
                      className="block text-sm font-medium text-gray-700"
                    >
                      Maximum File Size
                    </label>
                    <span className="text-sm text-gray-500">{fileSize} KB</span>
                  </div>
                  <input
                    type="range"
                    id="file-size-limit"
                    min="50"
                    max="2000"
                    value={fileSize}
                    onChange={(e) => setFileSize(Number(e.target.value))}
                    step="50"
                    className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer mt-2"
                  />
                  <p className="mt-1 text-xs text-gray-500">
                    Files larger than this will be skipped during analysis
                  </p>
                </div>
              </div>

              {/* Submit Button */}
              <div className="flex justify-end">
                <button
                  type="submit"
                  disabled={isAnalyzeDisabled}
                  className={`px-6 py-3 font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-tutorial-primary ${
                    isAnalyzeDisabled
                      ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                      : "bg-tutorial-primary text-white hover:bg-blue-700"
                  }`}
                >
                  <i className="fa-solid fa-flag-checkered mr-2"></i>
                  {isAnalyzeDisabled &&
                  trialStatus.isInTrial &&
                  !trialStatus.canCreateTutorial
                    ? "Upgrade to Continue"
                    : "Analyze Repository"}
                </button>
              </div>
            </form>
          )}
          {/* Repository Structure Preview */}
          {isCrawling && (
            <>
              <div className="mb-6">
                <div className="flex items-center justify-between mb-2">
                  <label className="block text-sm font-medium text-gray-700">
                    Repository Structure
                  </label>
                  <span className="text-xs text-gray-500">
                    Select files to include in analysis
                  </span>
                </div>

                <GitHubRepoCrawler
                  repoUrl={repoUrl}
                  githubToken={
                    isPrivateRepo
                      ? (
                          document.getElementById(
                            "github-token"
                          ) as HTMLInputElement
                        )?.value
                      : undefined
                  }
                  includePatterns={parsePatterns(
                    (
                      document.getElementById(
                        "include-patterns"
                      ) as HTMLInputElement
                    )?.value || DEFAULT_INCLUDE_PATTERNS.join(", ")
                  )}
                  excludePatterns={parsePatterns(
                    (
                      document.getElementById(
                        "exclude-patterns"
                      ) as HTMLInputElement
                    )?.value || DEFAULT_EXCLUDE_PATTERNS.join(", ")
                  )}
                  onSelectionChange={handleSelectedFilesChange}
                />
              </div>

              <form onSubmit={handleStartGeneration} onReset={handleReset}>
                {/* Repository URL Input */}

                {/* Tutorial Configuration Section */}
                <div className="mb-6">
                  <h3 className="text-sm font-medium text-gray-700 mb-3">
                    Tutorial Configuration
                  </h3>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    {/* Target Audience */}
                    <div>
                      <label
                        htmlFor="target-audience"
                        className="block text-sm font-medium text-gray-700 mb-1"
                      >
                        Target Audience
                      </label>
                      <select
                        id="target-audience"
                        defaultValue="intermediate"
                        className="block w-full py-2 border border-gray-300 rounded-md focus:ring-tutorial-primary focus:border-tutorial-primary"
                      >
                        {/* <option value="beginner">Beginner (No prior knowledge)</option> */}
                        <option value="intermediate">
                          Intermediate (Some familiarity)
                        </option>
                        {/* <option value="advanced">Advanced (Experienced developers)</option> */}
                      </select>
                    </div>

                    {/* Content Language */}
                    <div>
                      <label
                        htmlFor="content-language"
                        className="block text-sm font-medium text-gray-700 mb-1"
                      >
                        Content Language
                      </label>
                      <select
                        id="content-language"
                        defaultValue="auto"
                        className="block w-full py-2 border border-gray-300 rounded-md focus:ring-tutorial-primary focus:border-tutorial-primary"
                      >
                        <option value="english">English</option>
                        <option value="german">German</option>
                        <option value="italian">Italian</option>
                        <option value="chinese">Chinese</option>
                      </select>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    {/* Max Abstractions */}
                    <div>
                      <label
                        htmlFor="max-abstractions"
                        className="block text-sm font-medium text-gray-700 mb-1"
                      >
                        Max Core Concepts
                      </label>
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <i className="fa-solid fa-layer-group text-gray-400"></i>
                        </div>
                        <input
                          type="number"
                          id="max-abstractions"
                          defaultValue="10"
                          min="3"
                          max="15"
                          className="block w-full pl-10 py-2 border border-gray-300 rounded-md focus:ring-tutorial-primary focus:border-tutorial-primary"
                        />
                      </div>
                      <p className="mt-1 text-xs text-gray-500">
                        Number of core abstractions to identify (3-15)
                      </p>
                    </div>

                    {/* Tutorial Format */}
                    <div>
                      <label
                        htmlFor="tutorial-format"
                        className="block text-sm font-medium text-gray-700 mb-1"
                      >
                        Output Format
                      </label>
                      <select
                        id="tutorial-format"
                        defaultValue="markdown"
                        className="block w-full py-2 border border-gray-300 rounded-md focus:ring-tutorial-primary focus:border-tutorial-primary"
                      >
                        <option value="markdown">Markdown</option>
                        {/* <option value="html">HTML</option>
                      <option value="pdf">PDF</option> */}
                      </select>
                    </div>
                  </div>

                  {/* Additional Options */}
                  <div className="space-y-2">
                    <div className="flex items-center">
                      <input
                        disabled
                        type="checkbox"
                        id="include-diagrams"
                        defaultChecked
                        className="h-4 w-4 text-tutorial-primary focus:ring-tutorial-primary border-gray-300 rounded"
                      />
                      <label
                        htmlFor="include-diagrams"
                        className="ml-2 block text-sm text-gray-700"
                      >
                        Include architecture diagrams
                      </label>
                    </div>
                    <div className="flex items-center">
                      <input
                        disabled
                        type="checkbox"
                        id="include-examples"
                        defaultChecked
                        className="h-4 w-4 text-tutorial-primary focus:ring-tutorial-primary border-gray-300 rounded"
                      />
                      <label
                        htmlFor="include-examples"
                        className="ml-2 block text-sm text-gray-700"
                      >
                        Add practical code examples
                      </label>
                    </div>
                    <div className="flex items-center">
                      <input
                        title="Coming soon"
                        disabled
                        type="checkbox"
                        id="include-exercises"
                        className="h-4 w-4 text-tutorial-primary focus:ring-tutorial-primary border-gray-300 rounded"
                      />
                      <label
                        htmlFor="include-exercises"
                        className="ml-2 block text-sm text-gray-700"
                      >
                        Include exercises and challenges
                      </label>
                    </div>
                  </div>
                </div>

                {/* Advanced Options (Collapsible) */}
                <div className="mb-6">
                  <button
                    type="button"
                    onClick={() => setShowAdvancedOptions(!showAdvancedOptions)}
                    className="flex items-center text-sm text-gray-600 hover:text-gray-900"
                  >
                    <i
                      className={`fa-solid fa-caret-right mr-1 transition-transform ${
                        showAdvancedOptions ? "transform rotate-90" : ""
                      }`}
                    ></i>
                    Advanced Options
                  </button>

                  {showAdvancedOptions && (
                    <div className="mt-3 p-4 bg-gray-50 rounded-md border border-gray-200">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label
                            htmlFor="max-tokens"
                            className="block text-sm font-medium text-gray-700 mb-1"
                          >
                            Max Tokens per API Call
                          </label>
                          <input
                            disabled
                            type="number"
                            id="max-tokens"
                            defaultValue="100000"
                            min="1000"
                            max="200000"
                            className="block w-full py-2 border border-gray-300 rounded-md focus:ring-tutorial-primary focus:border-tutorial-primary"
                          />
                          <p className="mt-1 text-xs text-gray-500">
                            Controls chunking for large codebases
                          </p>
                        </div>

                        <div>
                          <label
                            htmlFor="model-version"
                            className="block text-sm font-medium text-gray-700 mb-1"
                          >
                            LLM Model
                          </label>
                          <select
                            disabled
                            id="model-version"
                            defaultValue="UX Pilot-3-7-sonnet"
                            className="block w-full py-2 border border-gray-300 rounded-md focus:ring-tutorial-primary focus:border-tutorial-primary"
                          >
                            <option value="auto">Auto</option>
                            {/* <option value="UX Pilot-3-7-sonnet">UX Pilot 3.7 Sonnet</option>
                          <option value="UX Pilot-3-opus">UX Pilot 3 Opus</option>
                          <option value="gpt-4o">GPT-4o</option> */}
                          </select>
                        </div>

                        <div>
                          <label
                            htmlFor="cache-duration"
                            className="block text-sm font-medium text-gray-700 mb-1"
                          >
                            Cache Duration
                          </label>
                          <select
                            disabled
                            id="cache-duration"
                            defaultValue="7"
                            className="block w-full py-2 border border-gray-300 rounded-md focus:ring-tutorial-primary focus:border-tutorial-primary"
                          >
                            <option value="1">1 day</option>
                            <option value="7">7 days</option>
                            <option value="30">30 days</option>
                            <option value="0">No caching</option>
                          </select>
                        </div>

                        <div>
                          <label
                            htmlFor="temperature"
                            className="block text-sm font-medium text-gray-700 mb-1"
                          >
                            Temperature: {temperature.toFixed(1)}
                          </label>
                          <input
                            type="range"
                            id="temperature"
                            min="0"
                            max="1"
                            step="0.1"
                            defaultValue="0.7"
                            className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                            onChange={(e) =>
                              setTemperature(Number(e.target.value))
                            }
                          />
                          <div className="flex justify-between text-xs text-gray-500 mt-1">
                            <span>Precise</span>
                            <span>Creative</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                <div className="flex justify-between space-x-4">
                  {/* Reset Button */}
                  <div className="flex justify-end ">
                    <Button variant="outline" type="reset">
                      <i className="fa-solid fa-left-long"></i>
                      Reset
                    </Button>
                  </div>
                  {/* Submit Button */}
                  <div className="flex justify-end">
                    <Button
                      type="submit"
                      disabled={isGenerateDisabled}
                      className={`px-6 py-3 font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-tutorial-primary ${
                        isGenerateDisabled
                          ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                          : "bg-tutorial-primary text-white"
                      }`}
                    >
                      <i className="fa-solid fa-wand-magic-sparkles mr-2"></i>
                      {isGenerateDisabled
                        ? "Upgrade to Continue"
                        : "Generate Tutorial"}
                    </Button>
                  </div>
                </div>
              </form>
            </>
          )}
          {/* <div>
             <SubscriptionStatus />
           </div>
                    {!subscriptionLoading && !subscribed && (
           <div className="mt-12">
             <h2 className="text-2xl font-bold mb-6 text-center">Upgrade Your Plan</h2>
             <SubscriptionPlans />
           </div> 
         )}
           */}
        </div>

        {/* Right Sidebar */}
        <div className="w-full lg:w-1/3 space-y-6">
          {/* Recently Generated */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-xl font-semibold mb-4 text-gray-800">
              Recently Generated
            </h2>

            <div className="space-y-4">
              <Link
                to="/gallery"
                className="block border border-gray-200 rounded-md p-3 hover:bg-gray-50 transition-colors"
              >
                <div className="flex items-center mb-2">
                  <i className="fa-brands fa-react text-blue-500 text-lg mr-2"></i>
                  <h3 className="font-medium text-gray-800">react-query</h3>
                </div>
                <p className="text-sm text-gray-600 mb-2">
                  A data fetching library for React applications
                </p>
                <div className="flex items-center text-xs text-gray-500">
                  <span>10 chapters</span>
                  <span className="mx-2">•</span>
                  <span>Generated 2 hours ago</span>
                </div>
              </Link>

              <Link
                to="/gallery"
                className="block border border-gray-200 rounded-md p-3 hover:bg-gray-50 transition-colors"
              >
                <div className="flex items-center mb-2">
                  <i className="fa-brands fa-python text-green-600 text-lg mr-2"></i>
                  <h3 className="font-medium text-gray-800">
                    fastapi-tutorial
                  </h3>
                </div>
                <p className="text-sm text-gray-600 mb-2">
                  Modern, fast web framework for building APIs with Python
                </p>
                <div className="flex items-center text-xs text-gray-500">
                  <span>8 chapters</span>
                  <span className="mx-2">•</span>
                  <span>Generated yesterday</span>
                </div>
              </Link>

              <Link
                to="/gallery"
                className="block border border-gray-200 rounded-md p-3 hover:bg-gray-50 transition-colors"
              >
                <div className="flex items-center mb-2">
                  <i className="fa-brands fa-node-js text-yellow-600 text-lg mr-2"></i>
                  <h3 className="font-medium text-gray-800">express-starter</h3>
                </div>
                <p className="text-sm text-gray-600 mb-2">
                  Minimal Express.js web application framework
                </p>
                <div className="flex items-center text-xs text-gray-500">
                  <span>6 chapters</span>
                  <span className="mx-2">•</span>
                  <span>Generated 3 days ago</span>
                </div>
              </Link>
            </div>

            <Link
              to="/gallery"
              className="block text-center text-sm text-tutorial-primary hover:text-blue-700 mt-4"
            >
              View all generated tutorials →
            </Link>
          </div>

          {/* How It Works */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-xl font-semibold mb-4 text-gray-800">
              How It Works
            </h2>

            <div className="space-y-4">
              <div className="flex">
                <div className="flex-shrink-0 flex items-center justify-center w-8 h-8 rounded-full bg-blue-100 text-tutorial-primary mr-3">
                  1
                </div>
                <div>
                  <h3 className="font-medium text-gray-800 mb-1">
                    Repository Analysis
                  </h3>
                  <p className="text-sm text-gray-600">
                    We fetch and analyze the code structure from your GitHub
                    repository
                  </p>
                </div>
              </div>

              <div className="flex">
                <div className="flex-shrink-0 flex items-center justify-center w-8 h-8 rounded-full bg-blue-100 text-tutorial-primary mr-3">
                  2
                </div>
                <div>
                  <h3 className="font-medium text-gray-800 mb-1">
                    Concept Identification
                  </h3>
                  <p className="text-sm text-gray-600">
                    Our AI identifies core abstractions and relationships
                  </p>
                </div>
              </div>

              <div className="flex">
                <div className="flex-shrink-0 flex items-center justify-center w-8 h-8 rounded-full bg-blue-100 text-tutorial-primary mr-3">
                  3
                </div>
                <div>
                  <h3 className="font-medium text-gray-800 mb-1">
                    Content Generation
                  </h3>
                  <p className="text-sm text-gray-600">
                    Detailed explanations, examples, and diagrams are created
                  </p>
                </div>
              </div>

              <div className="flex">
                <div className="flex-shrink-0 flex items-center justify-center w-8 h-8 rounded-full bg-blue-100 text-tutorial-primary mr-3">
                  4
                </div>
                <div>
                  <h3 className="font-medium text-gray-800 mb-1">
                    Tutorial Assembly
                  </h3>
                  <p className="text-sm text-gray-600">
                    Everything is compiled into a complete, navigable tutorial
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

)}

    </>
  );
};

export default Create;

// import React from "react";
// import { SubscriptionStatus } from "@/components/SubscriptionStatus";
// import { SubscriptionPlans } from "@/components/SubscriptionPlans";
// import { useSubscription } from "@/hooks/useSubscription";
// import GitHubRepoCrawler from "@/components/GitHubRepoCrawler";
// import TrialStatusBanner from "@/components/TrialStatusBanner";

// const Dashboard = () => {
//   const { subscribed, loading } = useSubscription();

//   return (
//     <div className="container mx-auto px-4 py-6">
//       <TrialStatusBanner />

//       <div className="mb-8">
//         <h1 className="text-3xl font-bold mb-6">Dashboard</h1>

//         <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
//           <div className="lg:col-span-2">
//             <GitHubRepoCrawler
//               repoUrl="https://github.com/octocat/Hello-World"
//               includePatterns={["*.js", "*.ts", "*.tsx", "*.jsx", "*.md"]}
//               excludePatterns={["node_modules/**", "*.test.*", "*.spec.*"]}
//               onSelectionChange={(files) => console.log("Selected files:", files)}
//             />
//           </div>
//           <div>
//             <SubscriptionStatus />
//           </div>
//         </div>

//         {!loading && !subscribed && (
//           <div className="mt-12">
//             <h2 className="text-2xl font-bold mb-6 text-center">Upgrade Your Plan</h2>
//             <SubscriptionPlans />
//           </div>
//         )}
//       </div>
//     </div>
//   );
// };

// export default Dashboard;
